import 'package:flutter/material.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../astreal.dart';
import '../../data/models/astrology/daily_astrology.dart';
import '../../data/services/api/astrology_service.dart';
import '../../data/services/api/birth_data_service.dart';
import '../../data/services/api/firebase_auth_service.dart';
import '../../data/services/astrology/daily_astrology_service.dart';
import 'ai_interpretation_result_page.dart';
import 'chart_page.dart';

/// 每日星相頁面
class DailyAstrologyPage extends StatefulWidget {
  final DateTime? initialDate;

  const DailyAstrologyPage({
    super.key,
    this.initialDate,
  });

  @override
  State<DailyAstrologyPage> createState() => _DailyAstrologyPageState();
}

class _DailyAstrologyPageState extends State<DailyAstrologyPage> {
  DateTime _selectedDate = DateTime.now();
  DailyAstrologyData? _dailyAstrology;
  PersonalizedDailyAstrology? _personalizedAstrology;
  bool _isLoading = true;
  bool _showPersonalized = false;
  String _userMode = 'starmaster'; // 預設為占星師模式
  bool _isPlanetPositionsExpanded = true; // 行星位置展開狀態

  @override
  void initState() {
    super.initState();
    if (widget.initialDate != null) {
      _selectedDate = widget.initialDate!;
    }
    _loadUserMode();
    _initializeDateFormatting();
  }

  /// 載入用戶模式
  Future<void> _loadUserMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userMode = prefs.getString('user_mode') ?? 'starmaster';
      setState(() {
        _userMode = userMode;
      });
      logger.d('載入用戶模式: $userMode');
    } catch (e) {
      logger.e('載入用戶模式失敗: $e');
    }
  }

  /// 初始化日期格式化
  Future<void> _initializeDateFormatting() async {
    try {
      await initializeDateFormatting('zh_TW', null);
      await _loadDailyAstrology();
    } catch (e) {
      logger.e('初始化日期格式化失敗: $e');
      // 如果初始化失敗，仍然載入資料但不使用本地化格式
      await _loadDailyAstrology();
    }
  }

  /// 載入每日星相資料
  Future<void> _loadDailyAstrology() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 載入一般星相
      final dailyAstrology =
          await DailyAstrologyService.getDailyAstrology(_selectedDate);

      // 載入個人化星相（如果用戶已登入）
      PersonalizedDailyAstrology? personalizedAstrology;
      final currentUser = FirebaseAuthService.getCurrentUser();
      if (currentUser != null) {
        personalizedAstrology =
            await DailyAstrologyService.getPersonalizedDailyAstrology(
          currentUser.uid,
          _selectedDate,
        );
      }

      if (mounted) {
        setState(() {
          _dailyAstrology = dailyAstrology;
          _personalizedAstrology = personalizedAstrology;
          _showPersonalized = false; // personalizedAstrology != null;
          _isLoading = false;
        });
      }
    } catch (e) {
      logger.e('載入每日星相失敗: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('載入星相資料失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 選擇日期
  Future<void> _selectDate() async {
    try {
      final picked = await showDatePicker(
        context: context,
        initialDate: _selectedDate,
        firstDate: DateTime(0000), // 或 DateTime(0000) 真的非常早期
        lastDate: DateTime(9999),  // 或 DateTime(9999) 幾乎沒限制
        locale: const Locale('zh', 'TW'),
      );

      if (picked != null && picked != _selectedDate) {
        setState(() {
          _selectedDate = picked;
        });
        await _loadDailyAstrology();
      }
    } catch (e) {
      logger.e('選擇日期失敗: $e');
      // 如果本地化日期選擇器失敗，使用預設的
      final picked = await showDatePicker(
        context: context,
        initialDate: _selectedDate,
        firstDate: DateTime.now().subtract(const Duration(days: 30)),
        lastDate: DateTime.now().add(const Duration(days: 7)),
      );

      if (picked != null && picked != _selectedDate) {
        setState(() {
          _selectedDate = picked;
        });
        await _loadDailyAstrology();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_userMode == 'starlight' ? '今日星象運勢' : '每日星象分析'),
        backgroundColor: _userMode == 'starlight'
            ? AppColors.solarAmber
            : AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          // 個人化切換按鈕
          if (_personalizedAstrology != null)
            IconButton(
              icon: Icon(_showPersonalized ? Icons.person : Icons.public),
              onPressed: () {
                setState(() {
                  _showPersonalized = !_showPersonalized;
                });
              },
              tooltip: _showPersonalized ? '切換到一般星象' : '切換到個人化星象',
            ),
          // 日期選擇按鈕
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectDate,
            tooltip: '選擇日期',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDailyAstrology,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 日期標題
                    _buildDateHeader(),
                    const SizedBox(height: 20),

                    // 主要內容
                    if (_showPersonalized &&
                        _personalizedAstrology != null) ...[
                      _buildPersonalizedContent(),
                    ] else if (_dailyAstrology != null) ...[
                      _buildGeneralContent(),
                    ] else ...[
                      _buildEmptyState(),
                    ],
                  ],
                ),
              ),
            ),
    );
  }

  /// 構建日期標題
  Widget _buildDateHeader() {
    final isToday = _selectedDate.year == DateTime.now().year &&
        _selectedDate.month == DateTime.now().month &&
        _selectedDate.day == DateTime.now().day;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.solarAmber.withValues(alpha: 0.1),
            AppColors.solarAmber.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.solarAmber.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            isToday
                ? (_userMode == 'starlight' ? '今日運勢指南' : '今日星象分析')
                : _formatDate(_selectedDate),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          if (isToday) ...[
            const SizedBox(height: 8),
            Text(
              _formatDateWithWeekday(_selectedDate),
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
          // 查看星盤按鈕區域
          const SizedBox(height: 16),
          _buildChartViewButtons(),

          if (_showPersonalized && _personalizedAstrology != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: AppColors.royalIndigo.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                _userMode == 'starlight' ? '專屬運勢' : '個人化星象',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.royalIndigo,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建個人化內容
  Widget _buildPersonalizedContent() {
    final astrology = _personalizedAstrology!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 個人化訊息
        _buildMessageCard(
          title: _userMode == 'starlight' ? '你的專屬運勢' : '你的專屬星象',
          content: astrology.personalizedMessage,
          icon: Icons.person_outline,
          color: AppColors.royalIndigo,
        ),

        const SizedBox(height: 16),

        // 個人化解讀按鈕
        _buildInterpretationButton(
          title: _userMode == 'starlight' ? '我的專屬運勢解讀' : '個人化星象解讀',
          subtitle: _userMode == 'starlight' ? '基於你的個人星盤分析今日運勢' : '基於你的行運盤深度分析',
          icon: Icons.psychology_outlined,
          color: AppColors.royalIndigo,
          onPressed: () => _navigateToPersonalizedInterpretation(),
        ),

        const SizedBox(height: 16),

        // 個人相位
        if (astrology.personalAspects.isNotEmpty) ...[
          _buildPersonalAspectsCard(
            title: _userMode == 'starlight' ? '個人運勢重點' : '個人相位提醒',
            aspects: astrology.personalAspects,
          ),
          const SizedBox(height: 16),
        ],

        // 個人建議
        if (astrology.recommendations.isNotEmpty) ...[
          _buildListCard(
            title: _userMode == 'starlight' ? '今日建議' : '個人化建議',
            items: astrology.recommendations,
            icon: Icons.lightbulb_outline,
            color: Colors.green,
          ),
        ],

        const SizedBox(height: 50),
      ],
    );
  }

  /// 構建一般內容
  Widget _buildGeneralContent() {
    final astrology = _dailyAstrology!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 總體訊息
        _buildMessageCard(
          title: '今日星象概況',
          content: astrology.generalMessage,
          icon: Icons.public_outlined,
          color: AppColors.solarAmber,
        ),

        const SizedBox(height: 16),

        // 一般星象解讀按鈕
        _buildInterpretationButton(
          title: _userMode == 'starlight' ? '今日整體運勢解讀' : '天象盤解讀',
          subtitle:
              _userMode == 'starlight' ? '分析今日星象對大家的影響' : '深度分析今日天象對集體的影響',
          icon: Icons.public_outlined,
          color: AppColors.solarAmber,
          onPressed: () => _navigateToGeneralInterpretation(),
        ),

        const SizedBox(height: 16),

        // 星象事件
        if (astrology.events.isNotEmpty) ...[
          _buildEventsSection(),
        ],

        const SizedBox(height: 50),
      ],
    );
  }

  /// 構建星象事件區塊
  Widget _buildEventsSection() {
    final events = _dailyAstrology!.events;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '今日重要星象',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 12),
        ...events.map((event) => _buildEventCard(event)),

        // 行星位置區域
        _buildPlanetPositionsSection(),
      ],
    );
  }

  /// 構建事件卡片
  Widget _buildEventCard(DailyAstrologyEvent event) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getEventColor(event.type).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getEventIcon(event.type),
                  color: _getEventColor(event.type),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      event.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    if (event.tags.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Wrap(
                        spacing: 4,
                        children: event.tags
                            .map((tag) => Chip(
                                  label: Text(
                                    tag,
                                    style: const TextStyle(fontSize: 10),
                                  ),
                                  backgroundColor: _getEventColor(event.type)
                                      .withValues(alpha: 0.1),
                                  side: BorderSide.none,
                                  materialTapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                  visualDensity: VisualDensity.compact,
                                ))
                            .toList(),
                      ),
                    ],
                  ],
                ),
              ),
              // 優先級指示器
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color:
                      _getPriorityColor(event.priority).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '★' * event.priority,
                  style: TextStyle(
                    color: _getPriorityColor(event.priority),
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            event.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建訊息卡片
  Widget _buildMessageCard({
    required String title,
    required String content,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.textDark,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建列表卡片
  Widget _buildListCard({
    required String title,
    required List<String> items,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...items.map((item) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        item,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.textDark,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.stars_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '暫無星象資料',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '請稍後再試或選擇其他日期',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// 獲取事件圖標
  IconData _getEventIcon(DailyAstrologyEventType type) {
    switch (type) {
      case DailyAstrologyEventType.newMoon:
        return Icons.brightness_2;
      case DailyAstrologyEventType.fullMoon:
        return Icons.brightness_1;
      case DailyAstrologyEventType.moonSignChange:
        return Icons.nights_stay;
      case DailyAstrologyEventType.planetaryAspect:
        return Icons.compare_arrows;
      case DailyAstrologyEventType.retrogradeStart:
      case DailyAstrologyEventType.retrogradeEnd:
        return Icons.replay;
      case DailyAstrologyEventType.planetSignChange:
        return Icons.swap_horiz;
      case DailyAstrologyEventType.moonVoidOfCourse:
        return Icons.pause_circle_outline;
      default:
        return Icons.stars;
    }
  }

  /// 獲取事件顏色
  Color _getEventColor(DailyAstrologyEventType type) {
    switch (type) {
      case DailyAstrologyEventType.newMoon:
        return Colors.indigo;
      case DailyAstrologyEventType.fullMoon:
        return Colors.orange;
      case DailyAstrologyEventType.moonSignChange:
        return Colors.blue;
      case DailyAstrologyEventType.planetaryAspect:
        return Colors.purple;
      case DailyAstrologyEventType.retrogradeStart:
      case DailyAstrologyEventType.retrogradeEnd:
        return Colors.red;
      case DailyAstrologyEventType.planetSignChange:
        return Colors.green;
      case DailyAstrologyEventType.moonVoidOfCourse:
        return Colors.grey;
      default:
        return AppColors.solarAmber;
    }
  }

  /// 獲取優先級顏色
  Color _getPriorityColor(int priority) {
    switch (priority) {
      case 5:
        return Colors.red;
      case 4:
        return Colors.orange;
      case 3:
        return AppColors.solarAmber;
      case 2:
        return Colors.blue;
      case 1:
      default:
        return Colors.grey;
    }
  }

  /// 安全的日期格式化
  String _formatDate(DateTime date) {
    try {
      return DateFormat('yyyy年M月d日', 'zh_TW').format(date);
    } catch (e) {
      // 如果本地化格式化失敗，使用基本格式
      return DateFormat('yyyy-MM-dd').format(date);
    }
  }

  /// 安全的日期加星期格式化（包含時區）
  String _formatDateWithWeekday(DateTime date) {
    try {
      final dateStr = DateFormat('yyyy年M月d日 EEEE', 'zh_TW').format(date);
      final timezone = _getCurrentTimezone();
      return '$dateStr ($timezone)';
    } catch (e) {
      // 如果本地化格式化失敗，使用基本格式
      final weekdays = ['週一', '週二', '週三', '週四', '週五', '週六', '週日'];
      final weekday = weekdays[date.weekday - 1];
      final timezone = _getCurrentTimezone();
      return '${DateFormat('yyyy-MM-dd').format(date)} $weekday ($timezone)';
    }
  }

  /// 獲取當前時區
  String _getCurrentTimezone() {
    final now = DateTime.now();
    final offset = now.timeZoneOffset;
    final hours = offset.inHours;
    final minutes = (offset.inMinutes % 60).abs();

    String sign = hours >= 0 ? '+' : '-';
    String hoursStr = hours.abs().toString().padLeft(2, '0');
    String minutesStr = minutes.toString().padLeft(2, '0');

    return 'UTC$sign$hoursStr:$minutesStr';
  }

  /// 構建解讀按鈕
  Widget _buildInterpretationButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: color,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 導航到個人化星象解讀（行運盤）
  Future<void> _navigateToPersonalizedInterpretation() async {
    final currentUser = FirebaseAuthService.getCurrentUser();
    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('請先登入以使用個人化功能'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      // 獲取 HomeViewModel 中設定的用戶資料
      final birthData = await _getSelectedPersonBirthData();
      if (birthData == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('請先在首頁設定出生資料以使用個人化功能'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // 創建行運盤數據
      final chartData = ChartData(
        chartType: ChartType.transit,
        primaryPerson: birthData,
        specificDate: _selectedDate,
      );

      // 使用 ChartViewModel 重新計算星盤
      final chartViewModel = ChartViewModel.withChartData(
        initialChartData: chartData,
      );

      // 異步計算星盤
      await chartViewModel.calculateChart();

      // 導航到 AI 解讀結果頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => ChartViewModel.withChartData(
                initialChartData: chartViewModel.chartData),
            child: AIInterpretationResultPage(
              chartData: chartViewModel.chartData,
              interpretationTitle:
                  _userMode == 'starlight' ? '我的專屬運勢解讀' : '個人化星象解讀',
              subtitle: _userMode == 'starlight'
                  ? '${_formatDate(_selectedDate)} - ${birthData.name} 的今日運勢'
                  : '${_formatDate(_selectedDate)} - ${birthData.name} 的行運分析',
              suggestedQuestions: [
                _buildPersonalizedInterpretationPrompt(),
              ],
              autoExecuteFirstQuestion: true,
            ),
          ),
        ),
      );
    } catch (e) {
      logger.e('導航到個人化解讀失敗: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('載入個人化解讀失敗: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 導航到一般星象解讀（天象盤）
  Future<void> _navigateToGeneralInterpretation() async {
    try {
      // 獲取 HomeViewModel 中設定的用戶資料作為天象盤的地點參考
      final userBirthData = await _getSelectedPersonBirthData();

      // 創建天象盤數據（使用用戶地點或預設格林威治）
      final chartData = ChartData(
        chartType: ChartType.mundane,
        primaryPerson: BirthData(
          id: 'celestial_event',
          name: '天象盤',
          dateTime: DateTime(_selectedDate.year, _selectedDate.month,
              _selectedDate.day, 12, 0),
          latitude: userBirthData?.latitude ?? 25.0330,
          longitude: userBirthData?.longitude ?? 121.5654,
          birthPlace: userBirthData?.birthPlace ?? '台北市',
        ),
        specificDate: _selectedDate,
      );

      // 使用 ChartViewModel 重新計算星盤
      final chartViewModel = ChartViewModel.withChartData(
        initialChartData: chartData,
      );

      // 異步計算星盤
      await chartViewModel.calculateChart();

      // 導航到 AI 解讀結果頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => ChartViewModel.withChartData(
                initialChartData: chartViewModel.chartData),
            child: AIInterpretationResultPage(
              chartData: chartViewModel.chartData,
              interpretationTitle:
                  _userMode == 'starlight' ? '今日整體運勢解讀' : '天象盤解讀',
              subtitle: _userMode == 'starlight'
                  ? '${_formatDate(_selectedDate)} - ${userBirthData?.birthPlace ?? "台北市"}整體運勢'
                  : '${_formatDate(_selectedDate)} - ${userBirthData?.birthPlace ?? "格林威治"}天象分析',
              suggestedQuestions: [
                _buildGeneralInterpretationPrompt(),
              ],
              autoExecuteFirstQuestion: true,
            ),
          ),
        ),
      );
    } catch (e) {
      logger.e('導航到天象解讀失敗: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('載入天象解讀失敗: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 獲取 HomeViewModel 中設定的用戶資料
  Future<BirthData?> _getSelectedPersonBirthData() async {
    try {
      // 獲取 HomeViewModel 的 selectedPerson
      final homeViewModel = Provider.of<HomeViewModel>(context, listen: false);
      return homeViewModel.selectedPerson;
    } catch (e) {
      logger.e('獲取選中的用戶資料失敗: $e');
      // 如果無法獲取 HomeViewModel，回退到獲取本地選中的出生資料
      return await BirthDataService().getSelectedBirthData();
    }
  }

  /// 構建個人化解讀提示詞
  String _buildPersonalizedInterpretationPrompt() {
    if (_userMode == 'starlight') {
      // 初心者模式：使用簡單易懂的語言
      return '''
請為用戶提供今日個人運勢解讀，使用簡單易懂的語言：

### 分析重點：
1. **今日運勢**：分析今天對個人的整體影響
2. **愛情運勢**：感情方面的運勢和建議
3. **事業運勢**：工作和學習方面的狀況
4. **健康運勢**：身心健康需要注意的地方
5. **財運狀況**：金錢和投資方面的建議
6. **幸運提醒**：今日的幸運色彩、數字或方向

### 解讀要求：
- 使用親切溫暖的語言，像朋友般的建議
- 避免使用專業占星術語，用生活化的表達
- 提供具體可行的日常建議
- 重點關注正面引導和實用性
- 語言簡潔明瞭，容易理解

請提供溫暖實用的個人運勢指導。
''';
    } else {
      // 占星師模式：使用專業術語
      return '''
請基於這個行運盤為用戶提供詳細的個人化星象解讀分析：

### 分析重點：
1. **行運相位分析**：分析當前行運行星與本命行星的相位關係
2. **宮位影響**：分析行運行星經過的宮位對生活領域的影響
3. **星座能量**：分析行運行星所在星座的能量表現
4. **時間週期**：分析當前行運的時間週期和發展階段
5. **生活領域預測**：預測對愛情、事業、健康、財運等領域的具體影響
6. **心理狀態**：分析當前的心理狀態和情緒變化模式

### 解讀要求：
- 使用專業的占星術語和概念
- 詳細分析行運行星與本命盤的互動關係
- 提供深度的心理和靈性層面分析
- 結合古典和現代占星學理論
- 重點關注個人成長和意識發展

請提供專業深入的占星學解讀分析。
''';
    }
  }

  /// 構建一般解讀提示詞
  String _buildGeneralInterpretationPrompt() {
    if (_userMode == 'starlight') {
      // 初心者模式：使用簡單易懂的語言
      return '''
請為用戶提供今日整體運勢解讀，使用簡單易懂的語言：

### 分析重點：
1. **今日整體氛圍**：分析今天的整體能量和氛圍
2. **大家的共同感受**：今天大部分人可能會有的感受
3. **適合做的事情**：今天特別適合進行的活動
4. **需要注意的事項**：今天需要特別留意的地方
5. **情緒能量**：今天的情緒特色和建議
6. **社會趨勢**：對大環境的簡單觀察

### 解讀要求：
- 使用生活化的語言，避免專業術語
- 重點關注對日常生活的實際影響
- 提供簡單易懂的建議
- 語言溫暖親切，像朋友的提醒
- 著重正面引導和實用性

請提供溫暖實用的整體運勢指導。
''';
    } else {
      // 占星師模式：使用專業術語
      return '''
請基於這個天象盤為用戶提供詳細的天象解讀分析：

### 分析重點：
1. **天象配置分析**：分析當日主要的行星相位和星座配置
2. **集體意識影響**：預測對集體意識、社會情緒的影響
3. **宇宙能量流動**：分析當前宇宙能量的流動模式和週期
4. **行星週期意義**：結合行星週期分析當前階段的意義
5. **歷史週期對照**：參考類似天象配置的歷史意義
6. **靈性發展機會**：分析集體靈性成長的機會

### 解讀要求：
- 使用專業的占星學術語和概念
- 從宏觀宇宙學角度分析天象意義
- 結合古典和現代占星學理論
- 重點關注集體意識和社會發展趨勢
- 提供深度的靈性和哲學層面分析

請提供專業深入的天象學解讀分析。
''';
    }
  }

  /// 構建查看星盤按鈕區域
  Widget _buildChartViewButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 天象盤按鈕
        Expanded(
          child: _buildChartButton(
            title: _userMode == 'starlight' ? '查看星象圖' : '查看天象盤',
            subtitle: _userMode == 'starlight' ? '當日星體位置' : '當日星體配置',
            icon: Icons.public,
            color: AppColors.solarAmber,
            onTap: _navigateToMundaneChart,
          ),
        ),
        const SizedBox(width: 12),
        // 行運盤按鈕（僅在個人化模式下顯示）
        if (_showPersonalized)
          Expanded(
            child: _buildChartButton(
              title: _userMode == 'starlight' ? '查看個人運勢圖' : '查看行運盤',
              subtitle: _userMode == 'starlight' ? '個人運勢影響' : '個人行運影響',
              icon: Icons.person_outline,
              color: AppColors.royalIndigo,
              onTap: _navigateToTransitChart,
            ),
          ),
      ],
    );
  }

  /// 構建星盤按鈕
  Widget _buildChartButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到天象盤
  Future<void> _navigateToMundaneChart() async {
    try {
      // 獲取用戶資料作為地點參考
      final userBirthData = await _getSelectedPersonBirthData();

      // 創建天象盤數據
      final chartData = ChartData(
        chartType: ChartType.mundane,
        primaryPerson: BirthData(
          id: 'mundane_${_selectedDate.millisecondsSinceEpoch}',
          name: '天象盤',
          dateTime: DateTime(_selectedDate.year, _selectedDate.month,
              _selectedDate.day, 12, 0),
          latitude: userBirthData?.latitude ?? 25.0330,
          // 預設台北
          longitude: userBirthData?.longitude ?? 121.5654,
          birthPlace: userBirthData?.birthPlace ?? '台北市',
        ),
        specificDate: _selectedDate,
      );

      // 使用 ChartViewModel 計算星盤
      final chartViewModel = ChartViewModel.withChartData(
        initialChartData: chartData,
      );

      await chartViewModel.calculateChart();

      if (!mounted) return;

      // 導航到星盤頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider.value(
            value: chartViewModel,
            child: const ChartPage(),
          ),
        ),
      );
    } catch (e) {
      logger.e('導航到天象盤失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('載入天象盤失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 導航到行運盤
  Future<void> _navigateToTransitChart() async {
    try {
      // 獲取用戶出生資料
      final birthData = await _getSelectedPersonBirthData();
      if (birthData == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('請先設定出生資料以查看行運盤'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // 創建行運盤數據
      final chartData = ChartData(
        chartType: ChartType.transit,
        primaryPerson: birthData,
        specificDate: _selectedDate,
      );

      // 使用 ChartViewModel 計算星盤
      final chartViewModel = ChartViewModel.withChartData(
        initialChartData: chartData,
      );

      await chartViewModel.calculateChart();

      if (!mounted) return;

      // 導航到星盤頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider.value(
            value: chartViewModel,
            child: const ChartPage(),
          ),
        ),
      );
    } catch (e) {
      logger.e('導航到行運盤失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('載入行運盤失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 構建個人相位卡片（優化版）
  Widget _buildPersonalAspectsCard({
    required String title,
    required List<String> aspects,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.solarAmber.withValues(alpha: 0.1),
              AppColors.solarAmber.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 標題區域
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.stars_outlined,
                    color: AppColors.solarAmber,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 相位列表
            ...aspects.asMap().entries.map((entry) {
              final index = entry.key;
              final aspect = entry.value;
              return Container(
                margin: EdgeInsets.only(bottom: index < aspects.length - 1 ? 12 : 0),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.solarAmber.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    // 相位類型圖標
                    Container(
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: _getAspectColor(aspect),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 相位描述
                    Expanded(
                      child: Text(
                        aspect,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.textDark,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  /// 根據相位類型獲取顏色
  Color _getAspectColor(String aspect) {
    if (aspect.contains('合相')) {
      return Colors.purple;
    } else if (aspect.contains('對分')) {
      return Colors.red;
    } else if (aspect.contains('三分')) {
      return Colors.green;
    } else if (aspect.contains('四分')) {
      return Colors.orange;
    } else {
      return AppColors.solarAmber;
    }
  }

  /// 構建行星位置區域
  Widget _buildPlanetPositionsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題區域
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.solarAmber.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.public,
                    color: AppColors.solarAmber,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _userMode == 'starlight' ? '今日星體位置' : '行星位置',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                ),
                // 展開/收合按鈕
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () {
                      setState(() {
                        _isPlanetPositionsExpanded = !_isPlanetPositionsExpanded;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        _isPlanetPositionsExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                        color: AppColors.solarAmber,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 行星位置內容
          if (_isPlanetPositionsExpanded) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: FutureBuilder<List<PlanetPosition>>(
                future: _loadPlanetPositions(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(20),
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.solarAmber),
                        ),
                      ),
                    );
                  }

                  if (snapshot.hasError) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.grey[400],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '載入行星位置失敗',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  final planets = snapshot.data ?? [];
                  if (planets.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.grey[400],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '暫無行星位置資料',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return _buildPlanetPositionsList(planets);
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建行星位置列表
  Widget _buildPlanetPositionsList(List<PlanetPosition> planets) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // 表頭
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.solarAmber.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const SizedBox(width: 40), // 行星符號空間
                const Expanded(
                  flex: 2,
                  child: Text(
                    '行星',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDark,
                    ),
                  ),
                ),
                const Expanded(
                  flex: 3,
                  child: Text(
                    '星座',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDark,
                    ),
                  ),
                ),
                const Expanded(
                  flex: 2,
                  child: Text(
                    '度數',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDark,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
          // 行星列表
          ...planets.asMap().entries.map((entry) {
            final index = entry.key;
            final planet = entry.value;
            return _buildPlanetPositionItem(planet, index);
          }),
        ],
      ),
    );
  }

  /// 構建單個行星位置項目
  Widget _buildPlanetPositionItem(PlanetPosition planet, int index) {
    final signInfo = _getSignInfo(planet.longitude);
    final isEven = index % 2 == 0;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isEven ? Colors.white : Colors.grey[50],
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // 行星符號
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: _getPlanetColor(planet.name).withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(18),
              border: Border.all(
                color: _getPlanetColor(planet.name).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                planet.symbol,
                style: TextStyle(
                  fontSize: 18,
                  color: planet.color,
                  fontWeight: FontWeight.bold,
                  fontFamily: "astro_one_font",
                ),
              ),
            ),
          ),
          const SizedBox(width: 4),

          // 行星名稱
          Expanded(
            flex: 2,
            child: Text(
              planet.name,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textDark,
              ),
            ),
          ),

          // 星座信息
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Text(
                  signInfo.symbol,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: "astro_one_font",
                    color: signInfo.color,
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    signInfo.name,
                    style: TextStyle(
                      fontSize: 13,
                      color: signInfo.color,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 度數
          Expanded(
            flex: 2,
            child: Text(
              signInfo.formattedDegree!,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
                fontFamily: 'monospace',
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  /// 載入行星位置
  Future<List<PlanetPosition>> _loadPlanetPositions() async {
    try {
      // 使用格林威治作為標準位置計算行星位置
      final astrologyService = AstrologyService();

      // 先計算宮位數據
      final houses = await astrologyService.calculateHouses(
        _selectedDate,
        0, // 緯度
        0, // 經度
      );

      // 計算行星位置
      final planets = await astrologyService.calculatePlanetPositions(
        _selectedDate,
        latitude: 0, // 緯度
        longitude: 0, // 經度
         housesData: houses, // 傳遞宮位數據
      );

      logger.d('載入行星位置成功，共 ${planets.length} 顆天體');
      return planets;
    } catch (e) {
      logger.e('載入行星位置失敗: $e');
      // 返回一個基本的行星列表，避免顯示空數據
      return _getBasicPlanetPositions();
    }
  }

  /// 獲取基本的行星位置（當計算失敗時使用）
  List<PlanetPosition> _getBasicPlanetPositions() {
    final positions = <PlanetPosition>[];

    // 創建基本的行星位置數據
    final basicPlanets = [
      {'id': 0, 'name': '太陽', 'symbol': '☉', 'longitude': 0.0},
      {'id': 1, 'name': '月亮', 'symbol': '☽', 'longitude': 30.0},
      {'id': 2, 'name': '水星', 'symbol': '☿', 'longitude': 60.0},
      {'id': 3, 'name': '金星', 'symbol': '♀', 'longitude': 90.0},
      {'id': 4, 'name': '火星', 'symbol': '♂', 'longitude': 120.0},
      {'id': 5, 'name': '木星', 'symbol': '♃', 'longitude': 150.0},
      {'id': 6, 'name': '土星', 'symbol': '♄', 'longitude': 180.0},
      {'id': 7, 'name': '天王星', 'symbol': '♅', 'longitude': 210.0},
      {'id': 8, 'name': '海王星', 'symbol': '♆', 'longitude': 240.0},
      {'id': 9, 'name': '冥王星', 'symbol': '♇', 'longitude': 270.0},
    ];

    for (final planet in basicPlanets) {
      final longitude = planet['longitude'] as double;
      final signInfo = _getSignInfo(longitude);

      positions.add(PlanetPosition(
        id: planet['id'] as int,
        name: planet['name'] as String,
        symbol: planet['symbol'] as String,
        color: _getPlanetColor(planet['name'] as String),
        longitude: longitude,
        latitude: 0.0,
        distance: 1.0,
        longitudeSpeed: 0.0,
        latitudeSpeed: 0.0,
        distanceSpeed: 0.0,
        sign: signInfo.name,
        house: ((longitude / 30).floor() + 1),
      ));
    }

    return positions;
  }

  /// 獲取星座信息和度數
  ZodiacSign _getSignInfo(double longitude) {
    // 使用 ZodiacSign.fromLongitude 獲取帶度數信息的星座
    ZodiacSign zodiacSign = ZodiacSign.fromLongitude(longitude);

    return zodiacSign;
  }

  /// 獲取行星符號
  String _getPlanetSymbol(String planetName) {
    // 使用 ZodiacSymbols 的統一符號系統
    return ZodiacSymbols.getPlanetSymbol(planetName);
  }

  /// 獲取行星顏色
  Color _getPlanetColor(String planetName) {
    final colors = {
      '太陽': Colors.orange,
      '月亮': Colors.blue,
      '水星': Colors.green,
      '金星': Colors.pink,
      '火星': Colors.red,
      '木星': Colors.purple,
      '土星': Colors.brown,
      '天王星': Colors.cyan,
      '海王星': Colors.indigo,
      '冥王星': Colors.deepPurple,
      '北交點': Colors.grey,
      '南交點': Colors.grey,
      '凱龍星': Colors.teal,
    };

    return colors[planetName] ?? AppColors.royalIndigo;
  }
}
