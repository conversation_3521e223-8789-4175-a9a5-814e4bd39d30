import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../astreal.dart';

/// 增強的日期時間選擇器，支援手動輸入和滾動選擇
class EnhancedDateTimePicker extends StatefulWidget {
  final DateTime initialDateTime;
  final Function(DateTime) onDateTimeChanged;
  final Function(bool)? onTimeUncertaintyChanged;
  final String title;
  final DateTime? minDate;
  final DateTime? maxDate;
  final bool allowTimeUncertainty;
  final bool initialTimeUncertain;

  const EnhancedDateTimePicker({
    super.key,
    required this.initialDateTime,
    required this.onDateTimeChanged,
    this.onTimeUncertaintyChanged,
    this.title = '選擇日期和時間',
    this.minDate,
    this.maxDate,
    this.allowTimeUncertainty = true,
    this.initialTimeUncertain = false,
  });

  @override
  State<EnhancedDateTimePicker> createState() => _EnhancedDateTimePickerState();

  /// 顯示增強的日期時間選擇器
  static Future<Map<String, dynamic>?> show({
    required BuildContext context,
    required DateTime initialDateTime,
    String title = '選擇日期和時間',
    DateTime? minDate,
    DateTime? maxDate,
    bool allowTimeUncertainty = true,
    bool initialTimeUncertain = false,
  }) async {
    DateTime? selectedDateTime = initialDateTime; // 初始化為傳入的日期時間
    bool isTimeUncertain = initialTimeUncertain;

    final result = await showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: EnhancedDateTimePicker(
            initialDateTime: initialDateTime,
            title: title,
            minDate: minDate,
            maxDate: maxDate,
            allowTimeUncertainty: allowTimeUncertainty,
            initialTimeUncertain: initialTimeUncertain,
            onDateTimeChanged: (DateTime newDateTime) {
              selectedDateTime = newDateTime;
            },
            onTimeUncertaintyChanged: (bool uncertain) {
              isTimeUncertain = uncertain;
            },
          ),
        );
      },
    );

    // 如果用戶點擊確認按鈕，result會包含返回的數據
    if (result != null) {
      return result;
    }

    // 如果用戶取消或點擊外部關閉，返回null
    return null;
  }
}

class _EnhancedDateTimePickerState extends State<EnhancedDateTimePicker>
    with SingleTickerProviderStateMixin {
  late DateTime _selectedDateTime;
  late TabController _tabController;
  bool _isTimeUncertain = false;

  // 手動輸入控制器
  final _yearController = TextEditingController();
  final _monthController = TextEditingController();
  final _dayController = TextEditingController();
  final _hourController = TextEditingController();
  final _minuteController = TextEditingController();

  // 滾動控制器
  late FixedExtentScrollController _scrollYearController;
  late FixedExtentScrollController _scrollMonthController;
  late FixedExtentScrollController _scrollDayController;
  late FixedExtentScrollController _scrollHourController;
  late FixedExtentScrollController _scrollMinuteController;

  @override
  void initState() {
    super.initState();
    _selectedDateTime = widget.initialDateTime;
    _isTimeUncertain = widget.initialTimeUncertain;
    _tabController = TabController(length: 2, vsync: this);

    _initializeControllers();
  }

  void _initializeControllers() {
    // 初始化文字輸入控制器
    _yearController.text = _selectedDateTime.year.toString();
    _monthController.text = _selectedDateTime.month.toString().padLeft(2, '0');
    _dayController.text = _selectedDateTime.day.toString().padLeft(2, '0');
    _hourController.text = _selectedDateTime.hour.toString().padLeft(2, '0');
    _minuteController.text = _selectedDateTime.minute.toString().padLeft(2, '0');

    // 初始化滾動控制器
    final minYear = widget.minDate?.year ?? 1900;
    _scrollYearController = FixedExtentScrollController(
      initialItem: _selectedDateTime.year - minYear,
    );
    _scrollMonthController = FixedExtentScrollController(
      initialItem: _selectedDateTime.month - 1,
    );
    _scrollDayController = FixedExtentScrollController(
      initialItem: _selectedDateTime.day - 1,
    );
    _scrollHourController = FixedExtentScrollController(
      initialItem: _selectedDateTime.hour,
    );
    _scrollMinuteController = FixedExtentScrollController(
      initialItem: _selectedDateTime.minute,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    _hourController.dispose();
    _minuteController.dispose();
    _scrollYearController.dispose();
    _scrollMonthController.dispose();
    _scrollDayController.dispose();
    _scrollHourController.dispose();
    _scrollMinuteController.dispose();
    super.dispose();
  }

  void _updateDateTime() {
    try {
      final year = int.tryParse(_yearController.text);
      final month = int.tryParse(_monthController.text);
      final day = int.tryParse(_dayController.text);
      final hour = int.tryParse(_hourController.text);
      final minute = int.tryParse(_minuteController.text);

      // 檢查所有值是否有效
      if (year == null || month == null || day == null || hour == null || minute == null) {
        return; // 有空值或無效值，不更新
      }

      // 檢查範圍
      final minYear = widget.minDate?.year ?? 1900;
      final maxYear = widget.maxDate?.year ?? 2100;

      if (year < minYear || year > maxYear ||
          month < 1 || month > 12 ||
          day < 1 || day > 31 ||
          hour < 0 || hour > 23 ||
          minute < 0 || minute > 59) {
        return; // 超出基本範圍，不更新
      }

      // 檢查日期是否存在（如2月30日）
      if (!_isValidDay(day)) {
        return; // 日期不存在，不更新
      }

      final newDateTime = DateTime(year, month, day, hour, minute);

      // 驗證日期範圍
      if (widget.minDate != null && newDateTime.isBefore(widget.minDate!)) {
        return;
      }
      if (widget.maxDate != null && newDateTime.isAfter(widget.maxDate!)) {
        return;
      }

      setState(() {
        _selectedDateTime = newDateTime;
      });

      widget.onDateTimeChanged(_selectedDateTime);
    } catch (e) {
      // 忽略無效輸入
    }
  }

  void _updateFromScroll() {
    final minYear = widget.minDate?.year ?? 1900;
    final year = minYear + _scrollYearController.selectedItem;
    final month = _scrollMonthController.selectedItem + 1;
    final day = _scrollDayController.selectedItem + 1;
    final hour = _scrollHourController.selectedItem;
    final minute = _scrollMinuteController.selectedItem;

    try {
      final newDateTime = DateTime(year, month, day, hour, minute);
      setState(() {
        _selectedDateTime = newDateTime;
        _yearController.text = year.toString();
        _monthController.text = month.toString().padLeft(2, '0');
        _dayController.text = day.toString().padLeft(2, '0');
        _hourController.text = hour.toString().padLeft(2, '0');
        _minuteController.text = minute.toString().padLeft(2, '0');
      });
      
      widget.onDateTimeChanged(_selectedDateTime);
    } catch (e) {
      // 處理無效日期（如2月30日）
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 標題欄
        Container(
          padding: const EdgeInsets.fromLTRB(24, 20, 16, 20),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.schedule,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const Text(
                      '請選擇準確的出生日期和時間',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
                style: IconButton.styleFrom(
                  foregroundColor: AppColors.textSecondary,
                  backgroundColor: Colors.white.withValues(alpha: 0.8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),

        // 標籤頁
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            border: Border(
              bottom: BorderSide(
                color: Colors.grey[200]!,
                width: 1,
              ),
            ),
          ),
          child: TabBar(
            controller: _tabController,
            labelColor: AppColors.royalIndigo,
            unselectedLabelColor: AppColors.textSecondary,
            indicatorColor: AppColors.royalIndigo,
            indicatorWeight: 3,
            indicatorSize: TabBarIndicatorSize.label,
            labelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            tabs: const [
              Tab(
                icon: Icon(Icons.edit, size: 20),
                text: '手動輸入',
                height: 60,
              ),
              Tab(
                icon: Icon(Icons.tune, size: 20),
                text: '滾動選擇',
                height: 60,
              ),
            ],
          ),
        ),

        // 內容區域
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildManualInputTab(),
              _buildScrollPickerTab(),
            ],
          ),
        ),

        // 底部區域
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, -4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 時間不確定選項
              if (widget.allowTimeUncertainty) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: _isTimeUncertain
                        ? AppColors.solarAmber.withValues(alpha: 0.1)
                        : Colors.grey[50],
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: _isTimeUncertain
                          ? AppColors.solarAmber.withValues(alpha: 0.3)
                          : Colors.grey[300]!,
                      width: 1.5,
                    ),
                  ),
                  child: Row(
                    children: [
                      Checkbox(
                        value: _isTimeUncertain,
                        onChanged: (value) {
                          setState(() {
                            _isTimeUncertain = value ?? false;
                          });
                          widget.onTimeUncertaintyChanged?.call(_isTimeUncertain);
                        },
                        activeColor: AppColors.solarAmber,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '出生時間不確定',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppColors.textDark,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '不知道確切的出生時間',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => _showTimeUncertaintyInfo(),
                        icon: const Icon(
                          Icons.info_outline,
                          size: 20,
                        ),
                        style: IconButton.styleFrom(
                          foregroundColor: AppColors.solarAmber,
                          backgroundColor: AppColors.solarAmber.withValues(alpha: 0.1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // 按鈕區域
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: BorderSide(color: Colors.grey[400]!),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        '取消',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // 確保回調被調用
                        widget.onDateTimeChanged(_selectedDateTime);
                        widget.onTimeUncertaintyChanged?.call(_isTimeUncertain);

                        // 返回結果
                        Navigator.of(context).pop({
                          'dateTime': _selectedDateTime,
                          'isTimeUncertain': _isTimeUncertain,
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.royalIndigo,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        '確認',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建手動輸入標籤頁
  Widget _buildManualInputTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 當前選擇的日期時間顯示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.royalIndigo.withValues(alpha: 0.2),
                width: 1.5,
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.royalIndigo.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.calendar_today,
                        color: AppColors.royalIndigo,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      '當前選擇',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  '${_selectedDateTime.year}年${_selectedDateTime.month.toString().padLeft(2, '0')}月${_selectedDateTime.day.toString().padLeft(2, '0')}日',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  '${_selectedDateTime.hour.toString().padLeft(2, '0')}:${_selectedDateTime.minute.toString().padLeft(2, '0')}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.solarAmber,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 28),

          // 統一的日期時間輸入區域
          const Text(
            '日期時間輸入',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),

          // 年月日時分在同一列
          Row(
            children: [
              Expanded(
                flex: 3,
                child: _buildNumberInput(
                  controller: _yearController,
                  label: '年',
                  hint: '1990',
                  minValue: widget.minDate?.year ?? 0,
                  maxValue: widget.maxDate?.year ?? 3000,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: _buildNumberInput(
                  controller: _monthController,
                  label: '月',
                  hint: '01',
                  minValue: 1,
                  maxValue: 12,
                  padZero: true,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: _buildNumberInput(
                  controller: _dayController,
                  label: '日',
                  hint: '01',
                  minValue: 1,
                  maxValue: 31,
                  padZero: true,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: _buildNumberInput(
                  controller: _hourController,
                  label: '時',
                  hint: '00',
                  minValue: 0,
                  maxValue: 23,
                  padZero: true,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: _buildNumberInput(
                  controller: _minuteController,
                  label: '分',
                  hint: '00',
                  minValue: 0,
                  maxValue: 59,
                  padZero: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: 28),

          // 快捷按鈕
          _buildQuickButtons(),
        ],
      ),
    );
  }

  /// 構建數字輸入框
  Widget _buildNumberInput({
    required TextEditingController controller,
    required String label,
    required String hint,
    required int minValue,
    required int maxValue,
    bool padZero = false,
  }) {
    // 檢查當前值是否有效
    final currentValue = int.tryParse(controller.text);
    final hasError = currentValue == null ||
                     currentValue < minValue ||
                     currentValue > maxValue ||
                     (label == '日' && !_isValidDay(currentValue));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: hasError ? Colors.red[700] : AppColors.textDark,
            ),
            onTap: () {
              // 點擊時選中所有文字，方便重新輸入
              controller.selection = TextSelection(
                baseOffset: 0,
                extentOffset: controller.text.length,
              );
            },
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: Colors.grey[400],
                fontWeight: FontWeight.normal,
                fontSize: 14,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: hasError ? Colors.red[300]! : Colors.grey[200]!,
                  width: 1.5,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: hasError ? Colors.red : AppColors.royalIndigo,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(vertical: 14, horizontal: 8),
              filled: true,
              fillColor: hasError ? Colors.red.withValues(alpha: 0.05) : Colors.white,
            ),
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(4),
            ],
            onChanged: (value) {
              // 觸發重建以更新錯誤狀態
              setState(() {});

              // 如果輸入有效，更新日期時間
              final intValue = int.tryParse(value);
              if (intValue != null &&
                  intValue >= minValue &&
                  intValue <= maxValue &&
                  (label != '日' || _isValidDay(intValue))) {
                _updateDateTime();
              }
            },
          ),
        ),

        // 錯誤提示
        if (hasError && controller.text.isNotEmpty) ...[
          const SizedBox(height: 4),
          SizedBox(
            width: double.infinity,
            child: Text(
              _getErrorMessage(label, currentValue, minValue, maxValue),
              style: TextStyle(
                fontSize: 10,
                color: Colors.red[600],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ],
    );
  }

  /// 構建快捷按鈕
  Widget _buildQuickButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: AppColors.solarAmber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.flash_on,
                color: AppColors.solarAmber,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
            const Text(
              '快捷選擇',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.grey[200]!,
              width: 1,
            ),
          ),
          child: Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              _buildQuickButton('現在', Icons.access_time, () {
                final now = DateTime.now();
                _setDateTime(now);
              }),
              _buildQuickButton('今天 00:00', Icons.brightness_2, () {
                final today = DateTime.now();
                _setDateTime(DateTime(today.year, today.month, today.day, 0, 0));
              }),
              _buildQuickButton('今天 12:00', Icons.wb_sunny, () {
                final today = DateTime.now();
                _setDateTime(DateTime(today.year, today.month, today.day, 12, 0));
              }),
              _buildQuickButton('昨天 12:00', Icons.history, () {
                final yesterday = DateTime.now().subtract(const Duration(days: 1));
                _setDateTime(DateTime(yesterday.year, yesterday.month, yesterday.day, 12, 0));
              }),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建快捷按鈕
  Widget _buildQuickButton(String text, IconData icon, VoidCallback onPressed) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.royalIndigo.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: AppColors.royalIndigo,
                ),
                const SizedBox(width: 6),
                Text(
                  text,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 設置日期時間
  void _setDateTime(DateTime dateTime) {
    setState(() {
      _selectedDateTime = dateTime;
      _yearController.text = dateTime.year.toString();
      _monthController.text = dateTime.month.toString().padLeft(2, '0');
      _dayController.text = dateTime.day.toString().padLeft(2, '0');
      _hourController.text = dateTime.hour.toString().padLeft(2, '0');
      _minuteController.text = dateTime.minute.toString().padLeft(2, '0');
    });
    widget.onDateTimeChanged(_selectedDateTime);
  }



  /// 檢查日期是否有效（考慮年月的天數限制）
  bool _isValidDay(int day) {
    try {
      final year = int.tryParse(_yearController.text) ?? _selectedDateTime.year;
      final month = int.tryParse(_monthController.text) ?? _selectedDateTime.month;

      // 檢查該年月是否有這一天
      final testDate = DateTime(year, month, day);
      return testDate.year == year && testDate.month == month && testDate.day == day;
    } catch (e) {
      return false;
    }
  }

  /// 獲取錯誤訊息
  String _getErrorMessage(String label, int? value, int minValue, int maxValue) {
    if (value == null) {
      return '請輸入有效數字';
    }

    switch (label) {
      case '年':
        if (value < minValue) {
          return '年份不能小於 $minValue';
        } else if (value > maxValue) {
          return '年份不能大於 $maxValue';
        }
        break;
      case '月':
        if (value < 1) {
          return '月份不能小於 1';
        } else if (value > 12) {
          return '月份不能大於 12';
        }
        break;
      case '日':
        if (value < 1) {
          return '日期不能小於 1';
        } else if (value > 31) {
          return '日期不能大於 31';
        } else if (!_isValidDay(value)) {
          final year = int.tryParse(_yearController.text) ?? _selectedDateTime.year;
          final month = int.tryParse(_monthController.text) ?? _selectedDateTime.month;
          final daysInMonth = DateTime(year, month + 1, 0).day;
          return '$year年$month月只有$daysInMonth天';
        }
        break;
      case '時':
        if (value < 0) {
          return '小時不能小於 0';
        } else if (value > 23) {
          return '小時不能大於 23';
        }
        break;
      case '分':
        if (value < 0) {
          return '分鐘不能小於 0';
        } else if (value > 59) {
          return '分鐘不能大於 59';
        }
        break;
    }

    return '輸入值無效';
  }

  /// 構建滾動選擇標籤頁
  Widget _buildScrollPickerTab() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      child: Column(
        children: [
          // 當前選擇顯示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.solarAmber.withValues(alpha: 0.2),
                width: 1.5,
              ),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: AppColors.solarAmber.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.tune,
                        color: AppColors.solarAmber,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      '滾動選擇',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  '${_selectedDateTime.year}年${_selectedDateTime.month.toString().padLeft(2, '0')}月${_selectedDateTime.day.toString().padLeft(2, '0')}日',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.solarAmber,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_selectedDateTime.hour.toString().padLeft(2, '0')}:${_selectedDateTime.minute.toString().padLeft(2, '0')}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // 滾動選擇器 - 年月日時分在同一列
          Expanded(
            child: Row(
              children: [
                // 年份
                Expanded(
                  flex: 3,
                  child: _buildScrollPicker(
                    controller: _scrollYearController,
                    items: _generateYearItems(),
                    label: '年',
                    onSelectedItemChanged: (index) => _updateFromScroll(),
                  ),
                ),
                const SizedBox(width: 6),
                // 月份
                Expanded(
                  flex: 2,
                  child: _buildScrollPicker(
                    controller: _scrollMonthController,
                    items: _generateMonthItems(),
                    label: '月',
                    onSelectedItemChanged: (index) => _updateFromScroll(),
                  ),
                ),
                const SizedBox(width: 6),
                // 日期
                Expanded(
                  flex: 2,
                  child: _buildScrollPicker(
                    controller: _scrollDayController,
                    items: _generateDayItems(),
                    label: '日',
                    onSelectedItemChanged: (index) => _updateFromScroll(),
                  ),
                ),
                const SizedBox(width: 6),
                // 小時
                Expanded(
                  flex: 2,
                  child: _buildScrollPicker(
                    controller: _scrollHourController,
                    items: _generateHourItems(),
                    label: '時',
                    onSelectedItemChanged: (index) => _updateFromScroll(),
                  ),
                ),
                const SizedBox(width: 6),
                // 分鐘
                Expanded(
                  flex: 2,
                  child: _buildScrollPicker(
                    controller: _scrollMinuteController,
                    items: _generateMinuteItems(),
                    label: '分',
                    onSelectedItemChanged: (index) => _updateFromScroll(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建滾動選擇器
  Widget _buildScrollPicker({
    required FixedExtentScrollController controller,
    required List<String> items,
    required String label,
    required Function(int) onSelectedItemChanged,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.royalIndigo.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: AppColors.royalIndigo,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey[200]!,
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: ListWheelScrollView.useDelegate(
                controller: controller,
                itemExtent: 36,
                physics: const FixedExtentScrollPhysics(),
                diameterRatio: 1.5,
                perspective: 0.003,
                onSelectedItemChanged: onSelectedItemChanged,
                childDelegate: ListWheelChildBuilderDelegate(
                  builder: (context, index) {
                    if (index < 0 || index >= items.length) return null;
                    final isSelected = index == controller.selectedItem;
                    return Container(
                      alignment: Alignment.center,
                      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppColors.royalIndigo.withValues(alpha: 0.1)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        items[index],
                        style: TextStyle(
                          fontSize: isSelected ? 16 : 14,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                          color: isSelected ? AppColors.royalIndigo : AppColors.textDark,
                        ),
                      ),
                    );
                  },
                  childCount: items.length,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 生成年份選項
  List<String> _generateYearItems() {
    final minYear = widget.minDate?.year ?? 1900;
    final maxYear = widget.maxDate?.year ?? 2100;
    return List.generate(
      maxYear - minYear + 1,
      (index) => (minYear + index).toString(),
    );
  }

  /// 生成月份選項
  List<String> _generateMonthItems() {
    return List.generate(12, (index) => (index + 1).toString().padLeft(2, '0'));
  }

  /// 生成日期選項
  List<String> _generateDayItems() {
    final daysInMonth = DateTime(_selectedDateTime.year, _selectedDateTime.month + 1, 0).day;
    return List.generate(daysInMonth, (index) => (index + 1).toString().padLeft(2, '0'));
  }

  /// 生成小時選項
  List<String> _generateHourItems() {
    return List.generate(24, (index) => index.toString().padLeft(2, '0'));
  }

  /// 生成分鐘選項
  List<String> _generateMinuteItems() {
    return List.generate(60, (index) => index.toString().padLeft(2, '0'));
  }

  /// 顯示時間不確定說明
  void _showTimeUncertaintyInfo() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 標題區域
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppColors.solarAmber.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.info_outline,
                        color: AppColors.solarAmber,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        '出生時間不確定的影響',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // 說明內容
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '當出生時間不確定時，會對占星分析產生以下影響：',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textDark,
                        ),
                      ),
                      SizedBox(height: 12),
                      _InfoItem(
                        icon: Icons.home,
                        title: '上升星座不準確',
                        description: '上升星座每2小時變化一次，時間不準確會影響整體性格分析',
                      ),
                      SizedBox(height: 8),
                      _InfoItem(
                        icon: Icons.location_on,
                        title: '宮位系統偏差',
                        description: '各行星所在宮位可能不準確，影響生活領域的分析',
                      ),
                      SizedBox(height: 8),
                      _InfoItem(
                        icon: Icons.schedule,
                        title: '時間敏感相位',
                        description: '月亮等快速移動的行星相位可能有誤差',
                      ),
                      SizedBox(height: 8),
                      _InfoItem(
                        icon: Icons.psychology,
                        title: '建議',
                        description: '可嘗試詢問家人或查看出生證明來確認準確時間',
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // 關閉按鈕
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.royalIndigo,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      '我了解了',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}



/// 信息項目組件
class _InfoItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;

  const _InfoItem({
    required this.icon,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: AppColors.royalIndigo.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            size: 16,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textDark,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
